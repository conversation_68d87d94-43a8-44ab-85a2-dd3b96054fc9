#!/usr/bin/env python3
"""
Test OCR functionality on block.png image
"""

import sys
import os

def check_dependencies():
    """Check if required libraries are available"""
    missing_deps = []
    
    try:
        import pytesseract
        print("✓ pytesseract is available")
    except ImportError:
        missing_deps.append("pytesseract")
        print("✗ pytesseract is missing")
    
    try:
        from PIL import Image, ImageEnhance, ImageFilter
        print("✓ Pillow (PIL) is available")
    except ImportError:
        missing_deps.append("Pillow")
        print("✗ Pillow (PIL) is missing")
    
    try:
        import cv2
        print("✓ opencv-python is available")
    except ImportError:
        missing_deps.append("opencv-python")
        print("✗ opencv-python is missing")
    
    try:
        import numpy as np
        print("✓ numpy is available")
    except ImportError:
        missing_deps.append("numpy")
        print("✗ numpy is missing")
    
    return missing_deps

def test_tesseract_path():
    """Test if Tesseract executable is accessible"""
    tesseract_path = r'C:\Users\<USER>\AppData\Local\Programs\Tesseract-OCR\tesseract.exe'
    
    if os.path.exists(tesseract_path):
        print(f"✓ Tesseract executable found at: {tesseract_path}")
        return True
    else:
        print(f"✗ Tesseract executable NOT found at: {tesseract_path}")
        return False

def test_image_file():
    """Test if the image file exists"""
    image_path = 'block.png'
    
    if os.path.exists(image_path):
        print(f"✓ Image file found: {image_path}")
        return True
    else:
        print(f"✗ Image file NOT found: {image_path}")
        return False

def perform_basic_ocr():
    """Perform basic OCR test if all dependencies are available"""
    try:
        import pytesseract
        from PIL import Image
        
        # Configure Tesseract path
        pytesseract.pytesseract.tesseract_cmd = r'C:\Users\<USER>\AppData\Local\Programs\Tesseract-OCR\tesseract.exe'
        
        # Load image
        image = Image.open('block.png')
        print(f"Image loaded successfully - Size: {image.size}, Mode: {image.mode}")
        
        # Perform OCR
        text = pytesseract.image_to_string(image)
        print("\n" + "="*50)
        print("RAW OCR OUTPUT:")
        print("="*50)
        print(repr(text))
        print("\n" + "="*50)
        print("FORMATTED OCR OUTPUT:")
        print("="*50)
        print(text)
        
        return text
        
    except Exception as e:
        print(f"Error performing OCR: {e}")
        return None

def main():
    print("OCR Test Script")
    print("="*50)
    
    # Check dependencies
    print("\n1. Checking Python dependencies...")
    missing_deps = check_dependencies()
    
    # Check Tesseract
    print("\n2. Checking Tesseract executable...")
    tesseract_ok = test_tesseract_path()
    
    # Check image file
    print("\n3. Checking image file...")
    image_ok = test_image_file()
    
    # Perform OCR if everything is available
    if not missing_deps and tesseract_ok and image_ok:
        print("\n4. All dependencies available. Performing OCR...")
        result = perform_basic_ocr()
        
        if result:
            print("\n" + "="*50)
            print("OCR TEST COMPLETED SUCCESSFULLY!")
            print("="*50)
        else:
            print("\n" + "="*50)
            print("OCR TEST FAILED!")
            print("="*50)
    else:
        print("\n4. Cannot perform OCR due to missing dependencies:")
        if missing_deps:
            print(f"   Missing Python packages: {', '.join(missing_deps)}")
        if not tesseract_ok:
            print("   Tesseract executable not found")
        if not image_ok:
            print("   Image file not found")
        
        print("\nTo install missing Python packages, run:")
        for dep in missing_deps:
            print(f"   pip install {dep}")

if __name__ == "__main__":
    main()
