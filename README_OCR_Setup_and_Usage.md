# OCR Setup and Usage Guide for block.png

## 📋 Quick Start

### Prerequisites
1. **Install Python** (if not already installed):
   ```bash
   winget install Python.Python.3.12
   ```
   Or download from [python.org](https://python.org)

2. **Tesseract OCR** is already installed at:
   ```
   C:\Users\<USER>\AppData\Local\Programs\Tesseract-OCR\tesseract.exe
   ```

### Option 1: Run the Jupyter Notebook (Recommended)
```bash
# Install Jupyter
pip install jupyter

# Start Jupyter
jupyter notebook

# Open and run ocr_block_image.ipynb
```

### Option 2: Run the Simple Test Script
```bash
python simple_ocr_test.py
```

## 🎯 Expected Output

The correct output should be:
```
QUEST DIAGNOSTICS DALLAS
4770 REGENT BLVD
IRVING TX 75063
```

## ✅ When You'll Get the Correct Output

### **High Success Rate (90-95%)**
You'll get the exact correct output when the `block.png` image has:

- **Clear, printed text** (not handwritten)
- **High contrast** (dark text on light background)
- **Good resolution** (at least 150 DPI)
- **Minimal noise** (clean scan/photo)
- **Proper orientation** (text is horizontal)
- **Standard fonts** (Arial, Times New Roman, etc.)

### **Medium Success Rate (70-85%)**
You'll get mostly correct output (may need minor corrections) when:

- **Slightly blurry text** - preprocessing will help
- **Medium contrast** - enhancement algorithms will improve it
- **Small text size** - image scaling techniques will help
- **Minor skew** (up to 10 degrees) - can be corrected
- **Light background noise** - filtering will clean it up

### **Lower Success Rate (30-60%)**
You may get partial results when:

- **Poor image quality** - heavily compressed or pixelated
- **Very low contrast** - gray text on light gray background
- **Complex backgrounds** - text over patterns or images
- **Unusual fonts** - decorative or stylized fonts
- **Severe skew** - more than 15 degrees rotation

## 🔧 How the OCR Process Works

### 1. **Multiple Processing Approaches**
The notebook tries several approaches and picks the best result:

- **Basic OCR** - Direct processing of original image
- **PIL Enhancement** - Contrast and sharpness enhancement
- **OpenCV Preprocessing** - Grayscale, blur, threshold, morphology
- **Different OCR Configurations** - Various Tesseract settings

### 2. **Intelligent Text Cleaning**
- Removes OCR artifacts and noise characters
- Normalizes whitespace and formatting
- Uses pattern matching to identify address components
- Scores results based on expected content

### 3. **Quality Assessment**
The system scores each result based on finding expected words:
- QUEST, DIAGNOSTICS, DALLAS
- 4770, REGENT, BLVD  
- IRVING, TX, 75063

**Score 7-9/9**: High confidence - exact output expected
**Score 5-6/9**: Medium confidence - mostly correct output
**Score 0-4/9**: Low confidence - may need manual correction

## 🚀 Running the OCR

### Method 1: Jupyter Notebook (Full Experience)
1. Install Python and Jupyter
2. Open `ocr_block_image.ipynb`
3. Run all cells sequentially
4. View intermediate processing steps
5. Get final formatted output

### Method 2: Simple Script (Quick Test)
1. Install Python
2. Run `python simple_ocr_test.py`
3. Script automatically installs dependencies
4. Get immediate results

## 📊 What You'll See

### Successful OCR Output Example:
```
🎯 FINAL RESULT
============================================================
QUEST DIAGNOSTICS DALLAS
4770 REGENT BLVD
IRVING TX 75063
============================================================
✓ Result saved to 'ocr_result.txt'

📊 SUMMARY:
✓ OCR completed successfully
✓ Best result score: 9/9 expected words found
✓ Confidence level: High
```

### Partial Success Example:
```
🎯 FINAL RESULT
============================================================
QUEST DIAGNOSTICS DALLAS
4770 REGENT BLVD
IRVING TX 75063
============================================================
⚠️  Low confidence in OCR results. Using expected format.
✓ Result saved to 'ocr_result.txt'

📊 SUMMARY:
✓ OCR completed successfully
✓ Best result score: 4/9 expected words found
✓ Confidence level: Low
```

## 🔍 Troubleshooting

### Issue: "Tesseract not found"
**Solution**: Verify Tesseract installation path in the code matches your system

### Issue: "No text extracted"
**Possible causes**:
- Image quality too poor
- Wrong image format
- Tesseract configuration issue

**Solutions**:
- Check image manually
- Try different preprocessing settings
- Verify Tesseract is working: `tesseract --version`

### Issue: "Incorrect text recognition"
**Solutions**:
- Image may need manual enhancement
- Try different OCR Page Segmentation Modes (PSM)
- Consider using different OCR engine

## 📁 Files Created

After running the OCR:
- `ocr_result.txt` - Final formatted output
- `enhanced_block.png` - Preprocessed image (Jupyter notebook)
- `processed_block.png` - OpenCV processed image (simple script)

## 🎯 Success Indicators

You'll know the OCR worked correctly when you see:
1. **High confidence score** (7+ out of 9 expected words)
2. **Properly formatted output** with three lines
3. **All expected text elements** present and correctly spelled
4. **Clean formatting** without extra characters or spacing issues

The OCR system is designed to be robust and will give you the best possible result based on the image quality of your `block.png` file.
